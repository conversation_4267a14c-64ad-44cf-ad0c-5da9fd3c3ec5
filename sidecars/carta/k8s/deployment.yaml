apiVersion: apps/v1
kind: Deployment
metadata:
  name: carta-auth-sidecar
  labels:
    app: carta-auth-sidecar
spec:
  replicas: 1
  selector:
    matchLabels:
      app: carta-auth-sidecar
  template:
    metadata:
      labels:
        app: carta-auth-sidecar
    spec:
      containers:
        - name: carta-auth-sidecar
          image: carta-auth-sidecar:local
          imagePullPolicy: IfNotPresent
          env:
            - name: TARGET_NAMESPACE
              value: "skaha-workload"
          ports:
            - name: http
              containerPort: 8000
          livenessProbe:
            httpGet:
              path: /livez
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /readyz
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          resources:
            requests:
              cpu: 50m
              memory: 64Mi
            limits:
              cpu: 200m
              memory: 256Mi

