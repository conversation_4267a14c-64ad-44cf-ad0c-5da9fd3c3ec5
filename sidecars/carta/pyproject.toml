[project]
name = "carta-sidecar"
version = "0.1.0"
description = "ForwardAuth service for CARTA sessions to resolve CANFAR UserID"
requires-python = ">=3.10"
readme = "README.md"
authors = [{ name = "CADC" }]
dependencies = [
  "fastapi>=0.112",
  "uvicorn[standard]>=0.30",
  "cachetools>=5.3",
  "kubernetes>=29.0",
  "typer>=0.17.3",
]

[tool.uv]
dev-dependencies = []
package = true

[project.scripts]
carta-sidecar = "uvicorn:main"
carta-intercept = "dev.intercept:main"
