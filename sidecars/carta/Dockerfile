# ---- builder: alpine with uv to resolve & install deps from pyproject ----
FROM python:3.13-slim AS builder

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# System deps for compiling wheels commonly needed by kubernetes deps
RUN apt-get update -y && apt-get install -y --no-install-recommends \
    curl ca-certificates build-essential libffi-dev libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Install uv (standalone installer)
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:/root/.cargo/bin:${PATH}"

WORKDIR /app

# Copy project manifest for dependency resolution layer caching
COPY pyproject.toml README.md ./

# Create venv and install all deps from pyproject with uv sync
RUN uv venv /opt/venv \
 && . /opt/venv/bin/activate \
 && uv sync --frozen

# ---- final runtime image (alpine) ----
FROM python:3.13-slim

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# Copy venv from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

WORKDIR /app
COPY app.py /app/app.py

EXPOSE 8000

# Run uvicorn; dependencies come from venv resolved via uv sync
CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000"]
